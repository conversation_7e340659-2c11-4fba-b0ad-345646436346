import axios from 'axios';
import * as cheerio from 'cheerio';
import { prisma } from '../lib/db.server.js';
import { format, parse } from 'date-fns';

export interface DomainData {
  domainName: string;
  expirationDate: Date;
  domainAge: string;
}

export class DomainScraper {
  private readonly url = process.env.SIMPLY_URL || 'https://www.simply.com/dk/ninja/';

  async scrapeExpiredDomains(): Promise<DomainData[]> {
    try {
      console.log('Starting domain scraping from:', this.url);
      
      const response = await axios.get(this.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 30000
      });

      const $ = cheerio.load(response.data);
      const domains: DomainData[] = [];

      // Find the table with the specified classes
      const table = $('.table-responsive .table.table-xs.table-hover.table-striped');
      
      if (table.length === 0) {
        console.warn('Table not found with specified classes');
        return domains;
      }

      // Parse table rows
      table.find('tbody tr').each((index, element) => {
        try {
          const $row = $(element);
          const cells = $row.find('td');
          
          if (cells.length >= 3) {
            const domainName = $(cells[0]).text().trim();
            const expirationText = $(cells[1]).text().trim();
            const domainAge = $(cells[2]).text().trim();

            if (domainName && expirationText) {
              // Parse the expiration date (format: "02.06.2025 06:52")
              const expirationDate = this.parseExpirationDate(expirationText);
              
              if (expirationDate) {
                domains.push({
                  domainName,
                  expirationDate,
                  domainAge
                });
              }
            }
          }
        } catch (error) {
          console.error('Error parsing row:', error);
        }
      });

      console.log(`Scraped ${domains.length} domains`);
      return domains;
    } catch (error) {
      console.error('Error scraping domains:', error);
      throw error;
    }
  }

  private parseExpirationDate(dateString: string): Date | null {
    try {
      // Expected format: "02.06.2025 06:52"
      const parsed = parse(dateString, 'dd.MM.yyyy HH:mm', new Date());
      return isNaN(parsed.getTime()) ? null : parsed;
    } catch (error) {
      console.error('Error parsing date:', dateString, error);
      return null;
    }
  }

  private calculateDomainRating(domainName: string): number {
    let rating = 0;
    
    // Higher rating for shorter domains
    if (domainName.length <= 3) {
      rating += 100;
    } else if (domainName.length <= 5) {
      rating += 50;
    } else if (domainName.length <= 8) {
      rating += 25;
    }
    
    // Bonus for no numbers
    if (!/\d/.test(domainName)) {
      rating += 20;
    }
    
    // Bonus for no hyphens
    if (!domainName.includes('-')) {
      rating += 10;
    }
    
    return rating;
  }

  async saveDomains(domains: DomainData[]): Promise<void> {
    const logEntry = await prisma.scrapingLog.create({
      data: {
        status: 'running',
        domainsFound: domains.length
      }
    });

    try {
      const collectionDate = new Date();
      
      for (const domain of domains) {
        const rating = this.calculateDomainRating(domain.domainName);
        const isHighlighted = domain.domainName.length <= 3;

        await prisma.domain.upsert({
          where: {
            domainName_collectionDate: {
              domainName: domain.domainName,
              collectionDate: new Date(collectionDate.toDateString())
            }
          },
          update: {
            expirationDate: domain.expirationDate,
            domainAge: domain.domainAge,
            domainRating: rating,
            isHighlighted
          },
          create: {
            domainName: domain.domainName,
            expirationDate: domain.expirationDate,
            domainAge: domain.domainAge,
            collectionDate: new Date(collectionDate.toDateString()),
            domainRating: rating,
            isHighlighted
          }
        });
      }

      await prisma.scrapingLog.update({
        where: { id: logEntry.id },
        data: {
          status: 'completed',
          endTime: new Date()
        }
      });

      console.log(`Successfully saved ${domains.length} domains to database`);
    } catch (error) {
      await prisma.scrapingLog.update({
        where: { id: logEntry.id },
        data: {
          status: 'failed',
          endTime: new Date(),
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      });
      throw error;
    }
  }

  async runScraping(): Promise<void> {
    try {
      const domains = await this.scrapeExpiredDomains();
      await this.saveDomains(domains);
    } catch (error) {
      console.error('Scraping failed:', error);
      throw error;
    }
  }
}

// CLI execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const scraper = new DomainScraper();
  scraper.runScraping()
    .then(() => {
      console.log('Scraping completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Scraping failed:', error);
      process.exit(1);
    });
}

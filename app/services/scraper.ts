import axios from 'axios';
import * as cheerio from 'cheerio';
import { prisma } from '../lib/db.server.js';
import { format, parse } from 'date-fns';

export interface DomainData {
  domainName: string;
  expirationDate: Date;
  domainAge: string;
}

export class DomainScraper {
  private readonly url = process.env.SIMPLY_URL || 'https://www.simply.com/dk/ninja/';

  async scrapeExpiredDomains(): Promise<DomainData[]> {
    try {
      console.log('🔍 Starting domain scraping from:', this.url);
      console.log('⏰ Timestamp:', new Date().toISOString());

      const response = await axios.get(this.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 30000
      });

      console.log('✅ HTTP Response received');
      console.log('📊 Response status:', response.status);
      console.log('📏 Response size:', response.data.length, 'characters');

      const $ = cheerio.load(response.data);
      const domains: DomainData[] = [];

      // Debug: Check page structure
      console.log('🔍 Analyzing page structure...');
      console.log('📄 Page title:', $('title').text().trim());

      // Look for different possible table selectors
      const possibleSelectors = [
        '.table-responsive .table.table-xs.table-hover.table-striped',
        '.table-responsive .table',
        '.table.table-xs.table-hover.table-striped',
        '.table-striped',
        'table',
        '.ninja-table',
        '#domains-table'
      ];

      let table = $();
      let usedSelector = '';

      for (const selector of possibleSelectors) {
        const foundTable = $(selector);
        console.log(`🔍 Checking selector "${selector}": found ${foundTable.length} elements`);

        if (foundTable.length > 0) {
          table = foundTable.first();
          usedSelector = selector;
          console.log(`✅ Using selector: "${selector}"`);
          break;
        }
      }

      if (table.length === 0) {
        console.warn('❌ No table found with any of the selectors');
        console.log('🔍 Available tables on page:');
        $('table').each((i, el) => {
          const tableClasses = $(el).attr('class') || 'no-class';
          const tableId = $(el).attr('id') || 'no-id';
          const rowCount = $(el).find('tr').length;
          console.log(`   Table ${i + 1}: class="${tableClasses}", id="${tableId}", rows=${rowCount}`);
        });

        // Save HTML for debugging
        console.log('💾 Saving page HTML for debugging...');
        require('fs').writeFileSync('debug-page.html', response.data);
        console.log('💾 Page saved as debug-page.html');

        return domains;
      }

      console.log(`✅ Found table with selector: "${usedSelector}"`);

      // Debug table structure
      const tableRows = table.find('tr');
      console.log(`📊 Total rows in table: ${tableRows.length}`);

      const headerRow = table.find('thead tr, tr').first();
      const headers: string[] = [];
      headerRow.find('th, td').each((i, el) => {
        headers.push($(el).text().trim());
      });
      console.log('📋 Table headers:', headers);

      // Parse table rows (skip header if exists)
      const dataRows = table.find('tbody tr').length > 0 ? table.find('tbody tr') : table.find('tr').slice(1);
      console.log(`📊 Data rows to process: ${dataRows.length}`);

      dataRows.each((index, element) => {
        try {
          const $row = $(element);
          const cells = $row.find('td, th');

          console.log(`🔍 Row ${index + 1}: ${cells.length} cells`);

          const cellTexts: string[] = [];
          cells.each((i, cell) => {
            cellTexts.push($(cell).text().trim());
          });
          console.log(`   Cell contents:`, cellTexts);

          if (cells.length >= 3) {
            const domainName = $(cells[0]).text().trim();
            const expirationText = $(cells[1]).text().trim();
            const domainAge = $(cells[2]).text().trim();

            console.log(`   🌐 Domain: "${domainName}"`);
            console.log(`   📅 Expiration: "${expirationText}"`);
            console.log(`   ⏳ Age: "${domainAge}"`);

            if (domainName && expirationText) {
              // Parse the expiration date (format: "02.06.2025 06:52")
              const expirationDate = this.parseExpirationDate(expirationText);

              if (expirationDate) {
                console.log(`   ✅ Parsed date: ${expirationDate.toISOString()}`);
                domains.push({
                  domainName,
                  expirationDate,
                  domainAge
                });
              } else {
                console.log(`   ❌ Failed to parse date: "${expirationText}"`);
              }
            } else {
              console.log(`   ⚠️ Missing domain name or expiration date`);
            }
          } else {
            console.log(`   ⚠️ Row has insufficient cells (${cells.length} < 3)`);
          }
        } catch (error) {
          console.error(`❌ Error parsing row ${index}:`, error);
        }
      });

      console.log(`🎉 Successfully scraped ${domains.length} domains`);

      if (domains.length > 0) {
        console.log('📋 Scraped domains summary:');
        domains.forEach((domain, i) => {
          console.log(`   ${i + 1}. ${domain.domainName} (expires: ${domain.expirationDate.toLocaleDateString()}, age: ${domain.domainAge})`);
        });
      }

      return domains;
    } catch (error) {
      console.error('❌ Error scraping domains:', error);
      if (error instanceof Error) {
        console.error('❌ Error details:', error.message);
        console.error('❌ Stack trace:', error.stack);
      }
      throw error;
    }
  }

  private parseExpirationDate(dateString: string): Date | null {
    try {
      // Expected format: "02.06.2025 06:52"
      const parsed = parse(dateString, 'dd.MM.yyyy HH:mm', new Date());
      return isNaN(parsed.getTime()) ? null : parsed;
    } catch (error) {
      console.error('Error parsing date:', dateString, error);
      return null;
    }
  }

  private calculateDomainRating(domainName: string): number {
    let rating = 0;
    
    // Higher rating for shorter domains
    if (domainName.length <= 3) {
      rating += 100;
    } else if (domainName.length <= 5) {
      rating += 50;
    } else if (domainName.length <= 8) {
      rating += 25;
    }
    
    // Bonus for no numbers
    if (!/\d/.test(domainName)) {
      rating += 20;
    }
    
    // Bonus for no hyphens
    if (!domainName.includes('-')) {
      rating += 10;
    }
    
    return rating;
  }

  async saveDomains(domains: DomainData[]): Promise<void> {
    const logEntry = await prisma.scrapingLog.create({
      data: {
        status: 'running',
        domainsFound: domains.length
      }
    });

    try {
      const collectionDate = new Date();
      
      for (const domain of domains) {
        const rating = this.calculateDomainRating(domain.domainName);
        const isHighlighted = domain.domainName.length <= 3;

        await prisma.domain.upsert({
          where: {
            domainName_collectionDate: {
              domainName: domain.domainName,
              collectionDate: new Date(collectionDate.toDateString())
            }
          },
          update: {
            expirationDate: domain.expirationDate,
            domainAge: domain.domainAge,
            domainRating: rating,
            isHighlighted
          },
          create: {
            domainName: domain.domainName,
            expirationDate: domain.expirationDate,
            domainAge: domain.domainAge,
            collectionDate: new Date(collectionDate.toDateString()),
            domainRating: rating,
            isHighlighted
          }
        });
      }

      await prisma.scrapingLog.update({
        where: { id: logEntry.id },
        data: {
          status: 'completed',
          endTime: new Date()
        }
      });

      console.log(`Successfully saved ${domains.length} domains to database`);
    } catch (error) {
      await prisma.scrapingLog.update({
        where: { id: logEntry.id },
        data: {
          status: 'failed',
          endTime: new Date(),
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      });
      throw error;
    }
  }

  async runScraping(): Promise<void> {
    try {
      const domains = await this.scrapeExpiredDomains();
      await this.saveDomains(domains);
    } catch (error) {
      console.error('Scraping failed:', error);
      throw error;
    }
  }
}

// CLI execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const scraper = new DomainScraper();
  scraper.runScraping()
    .then(() => {
      console.log('Scraping completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Scraping failed:', error);
      process.exit(1);
    });
}

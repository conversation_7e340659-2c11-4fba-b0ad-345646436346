import { type MetaFunction, type LoaderFunctionArgs } from "react-router";
import { useLoaderData, Link, useSearchParams, Form } from "react-router";
import { prisma } from "~/lib/db.server";
import { format, startOfDay, endOfDay, parseISO } from "date-fns";

export const meta: MetaFunction = () => {
  return [
    { title: "Domain List - Domains Expired Tracker" },
    { name: "description", content: "Browse expired domains by date with search and filtering" },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const dateParam = params.date || format(new Date(), 'yyyy-MM-dd');
  const search = url.searchParams.get("search") || "";
  const highlighted = url.searchParams.get("highlighted") === "true";
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = 50;

  try {
    const targetDate = parseISO(dateParam);
    let whereClause: any = {
      collectionDate: {
        gte: startOfDay(targetDate),
        lte: endOfDay(targetDate),
      },
    };

    if (search) {
      whereClause.domainName = {
        contains: search,
        mode: "insensitive",
      };
    }

    if (highlighted) {
      whereClause.isHighlighted = true;
    }

    const [domains, totalCount, availableDates] = await Promise.all([
      prisma.domain.findMany({
        where: whereClause,
        orderBy: [
          { isHighlighted: "desc" },
          { domainRating: "desc" },
          { domainName: "asc" },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.domain.count({ where: whereClause }),
      prisma.domain.findMany({
        select: {
          collectionDate: true,
        },
        distinct: ['collectionDate'],
        orderBy: {
          collectionDate: 'desc',
        },
        take: 30,
      }),
    ]);

    return Response.json({
      domains,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
      currentDate: dateParam,
      search,
      highlighted,
      availableDates: availableDates.map(d => format(d.collectionDate, 'yyyy-MM-dd')),
    });
  } catch (error) {
    console.error("Error loading domains:", error);
    return Response.json({
      domains: [],
      pagination: { page: 1, limit, total: 0, totalPages: 0 },
      currentDate: dateParam,
      search,
      highlighted,
      availableDates: [],
    });
  }
}

export default function Domains() {
  const data = useLoaderData<typeof loader>();
  const { domains, pagination, currentDate, search, highlighted, availableDates } = data || {
    domains: [],
    pagination: { page: 1, limit: 50, total: 0, totalPages: 0 },
    currentDate: new Date().toISOString().split('T')[0],
    search: '',
    highlighted: false,
    availableDates: []
  };
  const [searchParams, setSearchParams] = useSearchParams();

  const updateSearchParams = (updates: Record<string, string | null>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === "") {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });
    setSearchParams(newParams);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Domains for {format(new Date(currentDate), 'MMMM dd, yyyy')}
            </h1>
            <p className="text-gray-600 mt-1">
              {pagination.total} domains found
              {highlighted && " (highlighted only)"}
              {search && ` (filtered by "${search}")`}
            </p>
          </div>
          <Link
            to="/"
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← Back to Home
          </Link>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Date Selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date
              </label>
              <select
                value={currentDate}
                onChange={(e) => window.location.href = `/domains/${e.target.value}${window.location.search}`}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {availableDates.map((date) => (
                  <option key={date} value={date}>
                    {format(new Date(date), 'MMM dd, yyyy')}
                  </option>
                ))}
              </select>
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Domains
              </label>
              <input
                type="text"
                value={search}
                onChange={(e) => updateSearchParams({ search: e.target.value, page: "1" })}
                placeholder="Enter domain name..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Highlighted Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Filter
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={highlighted}
                  onChange={(e) => updateSearchParams({ 
                    highlighted: e.target.checked ? "true" : null,
                    page: "1"
                  })}
                  className="mr-2"
                />
                Highlighted only (≤3 chars)
              </label>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                onClick={() => updateSearchParams({ search: null, highlighted: null, page: "1" })}
                className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Domains List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Domain Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expiration Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Domain Age
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rating
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {domains.map((domain) => (
                  <tr 
                    key={domain.id}
                    className={domain.isHighlighted ? "bg-yellow-50" : ""}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={`text-sm font-medium ${
                          domain.isHighlighted ? "text-yellow-800" : "text-gray-900"
                        }`}>
                          {domain.domainName}
                        </span>
                        {domain.isHighlighted && (
                          <span className="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            ⭐ Highlighted
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {format(new Date(domain.expirationDate), 'MMM dd, yyyy HH:mm')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {domain.domainAge}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {domain.domainRating}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                {pagination.page > 1 && (
                  <button
                    onClick={() => updateSearchParams({ page: String(pagination.page - 1) })}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Previous
                  </button>
                )}
                {pagination.page < pagination.totalPages && (
                  <button
                    onClick={() => updateSearchParams({ page: String(pagination.page + 1) })}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Next
                  </button>
                )}
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(pagination.page * pagination.limit, pagination.total)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{pagination.total}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    {pagination.page > 1 && (
                      <button
                        onClick={() => updateSearchParams({ page: String(pagination.page - 1) })}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                      >
                        Previous
                      </button>
                    )}
                    {pagination.page < pagination.totalPages && (
                      <button
                        onClick={() => updateSearchParams({ page: String(pagination.page + 1) })}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                      >
                        Next
                      </button>
                    )}
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>

        {domains.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No domains found for the selected criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}

import { type LoaderFunctionArgs } from "react-router";
import { prisma } from "~/lib/db.server";
import { format, startOfDay, endOfDay, parseISO } from "date-fns";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const date = url.searchParams.get("date");
  const search = url.searchParams.get("search");
  const highlighted = url.searchParams.get("highlighted");
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "50");

  try {
    let whereClause: any = {};

    // Filter by date
    if (date) {
      const targetDate = parseISO(date);
      whereClause.collectionDate = {
        gte: startOfDay(targetDate),
        lte: endOfDay(targetDate),
      };
    }

    // Filter by search term
    if (search) {
      whereClause.domainName = {
        contains: search,
        mode: "insensitive",
      };
    }

    // Filter by highlighted domains
    if (highlighted === "true") {
      whereClause.isHighlighted = true;
    }

    const [domains, totalCount] = await Promise.all([
      prisma.domain.findMany({
        where: whereClause,
        orderBy: [
          { isHighlighted: "desc" },
          { domainRating: "desc" },
          { domainName: "asc" },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.domain.count({ where: whereClause }),
    ]);

    return Response.json({
      domains,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching domains:", error);
    return Response.json(
      { error: "Failed to fetch domains" },
      { status: 500 }
    );
  }
}

export async function action({ request }: LoaderFunctionArgs) {
  if (request.method === "POST") {
    // Manual trigger for scraping
    try {
      const { DomainScraper } = await import("~/services/scraper");
      const scraper = new DomainScraper();
      await scraper.runScraping();
      
      return Response.json({ success: true, message: "Scraping completed successfully" });
    } catch (error) {
      console.error("Manual scraping failed:", error);
      return Response.json(
        { error: "Scraping failed" },
        { status: 500 }
      );
    }
  }

  return Response.json({ error: "Method not allowed" }, { status: 405 });
}

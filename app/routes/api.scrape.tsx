import { type ActionFunctionArgs } from "react-router";
import { DomainScraper } from "~/services/scraper";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const scraper = new DomainScraper();
    await scraper.runScraping();

    return Response.json({
      success: true,
      message: "Domain scraping completed successfully"
    });
  } catch (error) {
    console.error("Scraping failed:", error);
    return Response.json(
      {
        error: "Scraping failed",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

export async function loader() {
  return Response.json({ error: "GET method not allowed" }, { status: 405 });
}

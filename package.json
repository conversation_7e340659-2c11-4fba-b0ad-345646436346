{"name": "domainsexpired", "version": "1.0.0", "description": "Track expired domains from Simply.com", "type": "module", "scripts": {"setup": "tsx scripts/setup.js", "dev": "react-router dev", "build": "react-router build", "start": "react-router-serve ./build/server/index.js", "typecheck": "tsc", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "scraper:run": "tsx app/services/scraper.ts", "scraper:test": "tsx scripts/test-scraper.js", "cron:start": "tsx app/services/cron.ts"}, "dependencies": {"@prisma/client": "^5.7.1", "@react-router/node": "^7.6.1", "@react-router/serve": "^7.6.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^7.6.1", "cheerio": "^1.0.0-rc.12", "node-cron": "^3.0.3", "axios": "^1.6.2", "date-fns": "^3.0.6", "clsx": "^2.0.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node-cron": "^3.0.11", "prisma": "^5.7.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "vite": "^5.0.10"}, "engines": {"node": ">=18.0.0"}}